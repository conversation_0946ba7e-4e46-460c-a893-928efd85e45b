// 背景脚本，在浏览器启动时运行

import { isMonitoredAPIRequest, getMonitoredAPIType, MONITORED_API_ENDPOINTS } from '@/services/chatgpt/chatgpt-utils';
import {
  MonitoredAPIMessageType,
  type MonitoredAPIRequestMessage,
  type MonitoredAPIHeadersMessage,
  type MonitoredAP<PERSON><PERSON>ponseHeadersMessage,
  type MonitoredAPICompletedMessage,
  type RequestBodyData,
  type RequestMetadata
} from '@/types/monitored-api';
import { browserControlService } from '@/services/browser-control';
import {
  isBrowserControlCommand,
  type BrowserControlCommand
} from '@/types/plugin-api';

console.log('ChatGPT Forward Plugin 背景脚本已加载');

// 网络请求拦截器
let isNetworkInterceptionActive = false;

// 启动网络拦截
function startNetworkInterception(): void {
  if (isNetworkInterceptionActive) {
    console.log('🔧 网络拦截器已经在运行中');
    return;
  }

  console.log('🔧 启动网络拦截器...');

  try {
    // 监听请求开始
    chrome.webRequest.onBeforeRequest.addListener(
      handleBeforeRequest,
      {
        urls: [
          "*://chatgpt.com/*",
          "*://chat.openai.com/*",
          "*://api.openai.com/*",
          "*://platform.openai.com/*"
        ]
      },
      ["requestBody"]
    );

    // 监听请求头
    chrome.webRequest.onBeforeSendHeaders.addListener(
      handleBeforeSendHeaders,
      {
        urls: [
          "*://chatgpt.com/*",
          "*://chat.openai.com/*",
          "*://api.openai.com/*",
          "*://platform.openai.com/*"
        ]
      },
      ["requestHeaders"]
    );

    // 监听响应头
    chrome.webRequest.onHeadersReceived.addListener(
      handleHeadersReceived,
      {
        urls: [
          "*://chatgpt.com/*",
          "*://chat.openai.com/*",
          "*://api.openai.com/*",
          "*://platform.openai.com/*"
        ]
      },
      ["responseHeaders"]
    );

    // 监听请求完成
    chrome.webRequest.onCompleted.addListener(
      handleCompleted,
      {
        urls: [
          "*://chatgpt.com/*",
          "*://chat.openai.com/*",
          "*://api.openai.com/*",
          "*://platform.openai.com/*"
        ]
      }
    );

    isNetworkInterceptionActive = true;
    console.log('✅ 网络拦截器启动成功');
  } catch (error) {
    console.error('❌ 启动网络拦截器失败:', error);
  }
}

// 停止网络拦截
function stopNetworkInterception(): void {
  if (!isNetworkInterceptionActive) return;

  console.log('🛑 停止网络拦截器...');

  try {
    chrome.webRequest.onBeforeRequest.removeListener(handleBeforeRequest);
    chrome.webRequest.onBeforeSendHeaders.removeListener(handleBeforeSendHeaders);
    chrome.webRequest.onHeadersReceived.removeListener(handleHeadersReceived);
    chrome.webRequest.onCompleted.removeListener(handleCompleted);

    isNetworkInterceptionActive = false;
    console.log('✅ 网络拦截器已停止');
  } catch (error) {
    console.error('❌ 停止网络拦截器失败:', error);
  }
}

/**
 * 解析请求体数据为JSON格式
 */
function parseRequestBody(requestBody: chrome.webRequest.UploadData[] | undefined): any {
  if (!requestBody || requestBody.length === 0) {
    return null;
  }

  try {
    // 处理第一个上传数据项
    const uploadData = requestBody[0];

    if (uploadData.bytes) {
      // 将ArrayBuffer转换为字符串
      const decoder = new TextDecoder('utf-8');
      const bodyText = decoder.decode(uploadData.bytes);

      // 尝试解析为JSON
      try {
        return JSON.parse(bodyText);
      } catch {
        // 如果不是JSON，返回原始文本
        return { raw_text: bodyText };
      }
    }

    if (uploadData.file) {
      return { file_upload: uploadData.file };
    }

    return null;
  } catch (error) {
    console.error('解析请求体失败:', error);
    return { parse_error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * 创建请求元数据
 */
function createRequestMetadata(url: string, source: string): RequestMetadata {
  return {
    endpoint_description: Object.values(MONITORED_API_ENDPOINTS).find(
      endpoint => url.includes(endpoint.pattern)
    )?.description || 'Unknown API endpoint',
    captured_at: new Date().toISOString(),
    source
  };
}

// 处理请求开始事件
function handleBeforeRequest(details: chrome.webRequest.WebRequestBodyDetails): void {
  // 只处理监控的API请求
  if (!isMonitoredAPIRequest(details.url)) return;

  const apiType = getMonitoredAPIType(details.url);
  console.log(`🌐 [WebRequest] 拦截到监控API请求 [${apiType}]:`, details.method, details.url);

  // 解析请求体
  const parsedRequestBody = parseRequestBody(details.requestBody?.formData ? undefined : details.requestBody?.raw);

  // 构建请求体数据
  const requestBodyData: RequestBodyData = {
    raw: parsedRequestBody,
    formData: details.requestBody?.formData || null,
    hasData: !!(details.requestBody?.raw || details.requestBody?.formData)
  };

  // 构建详细的JSON格式数据
  const requestMessage: MonitoredAPIRequestMessage = {
    type: MonitoredAPIMessageType.MONITORED_API_REQUEST,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: {
      api_type: apiType,
      api_endpoint: details.url,
      request: {
        url: details.url,
        method: details.method,
        requestId: details.requestId,
        tabId: details.tabId,
        frameId: details.frameId,
        requestBody: requestBodyData
      },
      metadata: createRequestMetadata(details.url, 'chrome_webrequest_api')
    }
  };

  // 转发到WebSocket服务器
  sendMessageToServer(requestMessage);
}

// 处理发送请求头事件
function handleBeforeSendHeaders(details: chrome.webRequest.WebRequestHeadersDetails): void {
  // 只处理监控的API请求
  if (!isMonitoredAPIRequest(details.url)) return;

  const apiType = getMonitoredAPIType(details.url);
  console.log(`📤 [WebRequest] 发送请求头 [${apiType}]:`, details.url);

  const headersMessage: MonitoredAPIHeadersMessage = {
    type: MonitoredAPIMessageType.MONITORED_API_HEADERS,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: {
      api_type: apiType,
      api_endpoint: details.url,
      request: {
        url: details.url,
        method: details.method,
        requestId: details.requestId,
        requestHeaders: details.requestHeaders
      },
      metadata: createRequestMetadata(details.url, 'chrome_webrequest_headers')
    }
  };

  sendMessageToServer(headersMessage);
}

// 处理接收响应头事件
function handleHeadersReceived(details: chrome.webRequest.WebResponseHeadersDetails): void {
  // 只处理监控的API请求
  if (!isMonitoredAPIRequest(details.url)) return;

  const apiType = getMonitoredAPIType(details.url);
  console.log(`📥 [WebRequest] 接收响应头 [${apiType}]:`, details.url);

  const responseHeadersMessage: MonitoredAPIResponseHeadersMessage = {
    type: MonitoredAPIMessageType.MONITORED_API_RESPONSE_HEADERS,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: {
      api_type: apiType,
      api_endpoint: details.url,
      response: {
        url: details.url,
        statusCode: details.statusCode,
        statusLine: details.statusLine,
        requestId: details.requestId,
        responseHeaders: details.responseHeaders
      },
      metadata: createRequestMetadata(details.url, 'chrome_webrequest_response')
    }
  };

  sendMessageToServer(responseHeadersMessage);
}

// 处理请求完成事件
function handleCompleted(details: chrome.webRequest.WebResponseHeadersDetails): void {
  // 只处理监控的API请求
  if (!isMonitoredAPIRequest(details.url)) return;

  const apiType = getMonitoredAPIType(details.url);
  console.log(`✅ [WebRequest] 请求完成 [${apiType}]:`, details.url);

  const completedMessage: MonitoredAPICompletedMessage = {
    type: MonitoredAPIMessageType.MONITORED_API_COMPLETED,
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: {
      api_type: apiType,
      api_endpoint: details.url,
      response: {
        url: details.url,
        statusCode: details.statusCode,
        statusLine: details.statusLine,
        requestId: details.requestId,
        responseHeaders: details.responseHeaders
      },
      timing: {
        timeStamp: details.timeStamp,
        completed_at: new Date().toISOString()
      },
      metadata: createRequestMetadata(details.url, 'chrome_webrequest_completed')
    }
  };

  sendMessageToServer(completedMessage);
}

// 获取当前活跃的ChatGPT标签页ID（不持久化）
async function getCurrentChatGPTTabId(): Promise<number | null> {
  try {
    console.log('开始查找ChatGPT标签页...');

    // 首先尝试获取当前活跃的标签页
    const activeTabs = await chrome.tabs.query({ active: true, currentWindow: true });
    console.log('当前活跃标签页:', activeTabs.map(tab => ({ id: tab.id, url: tab.url })));

    if (activeTabs.length > 0 && activeTabs[0].url &&
        (activeTabs[0].url.includes('chatgpt.com') || activeTabs[0].url.includes('chat.openai.com'))) {
      console.log('当前活跃标签页是ChatGPT页面:', activeTabs[0].id);
      return activeTabs[0].id || null;
    }

    // 如果当前活跃标签页不是ChatGPT，则查找任何ChatGPT标签页
    const chatgptTabs = await chrome.tabs.query({
      url: ['*://chatgpt.com/*', '*://chat.openai.com/*']
    });

    console.log('找到的ChatGPT标签页:', chatgptTabs.map(tab => ({ id: tab.id, url: tab.url })));

    if (chatgptTabs.length > 0) {
      console.log('使用第一个ChatGPT标签页:', chatgptTabs[0].id);
      return chatgptTabs[0].id || null;
    }

    console.log('没有找到任何ChatGPT标签页');
    return null;
  } catch (error) {
    console.error('获取ChatGPT标签页ID失败:', error);
    return null;
  }
}

// WebSocket连接状态枚举
enum ConnectionState {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

// WebSocket连接管理
let wsInstance: WebSocket | null = null;
let connectionState: ConnectionState = ConnectionState.DISCONNECTED;
let wsServerUrl = 'ws://localhost:8765';
let connectionTimeout: number | null = null;
let reconnectTimeout: number | null = null;
let reconnectAttempts: number = 0;
let maxReconnectAttempts: number = 10;
let isManualDisconnect: boolean = false;
let heartbeatInterval: number | null = null;
let heartbeatTimeout: number | null = null;
let lastHeartbeatTime: number = 0;
let isExtensionSuspended: boolean = false;

// 连接超时时间（毫秒）
const CONNECTION_TIMEOUT = 10000;
// 基础重连间隔（毫秒）
const BASE_RECONNECT_INTERVAL = 1000;
// 最大重连间隔（毫秒）
const MAX_RECONNECT_INTERVAL = 30000;
// 心跳间隔（毫秒）
const HEARTBEAT_INTERVAL = 30000;
// 心跳超时时间（毫秒）
const HEARTBEAT_TIMEOUT = 10000;

// 将连接状态转换为文本
function getStatusTextFromState(state: ConnectionState): string {
  switch (state) {
    case ConnectionState.DISCONNECTED:
      return '未连接';
    case ConnectionState.CONNECTING:
      return '连接中';
    case ConnectionState.CONNECTED:
      return '已连接';
    case ConnectionState.RECONNECTING:
      return '重连中';
    case ConnectionState.ERROR:
      return '连接失败';
    default:
      return '未知状态';
  }
}

// 图标状态管理
interface IconPaths {
  [size: string]: string;
  16: string;
  48: string;
  128: string;
}

const ICON_STATES = {
  CONNECTED: {
    16: 'icons/icon16-connected.png',
    48: 'icons/icon48-connected.png',
    128: 'icons/icon128-connected.png'
  } as IconPaths,
  DISCONNECTED: {
    16: 'icons/icon16-disconnected.png',
    48: 'icons/icon48-disconnected.png',
    128: 'icons/icon128-disconnected.png'
  } as IconPaths,
  CONNECTING: {
    16: 'icons/icon16-connecting.png',
    48: 'icons/icon48-connecting.png',
    128: 'icons/icon128-connecting.png'
  } as IconPaths,
  ERROR: {
    16: 'icons/icon16-error.png',
    48: 'icons/icon48-error.png',
    128: 'icons/icon128-error.png'
  } as IconPaths,
  DEFAULT: {
    16: 'icons/icon16.png',
    48: 'icons/icon48.png',
    128: 'icons/icon128.png'
  } as IconPaths
};

// 更新扩展图标
async function updateExtensionIcon(state: ConnectionState): Promise<void> {
  try {
    let iconPaths: IconPaths;

    switch (state) {
      case ConnectionState.CONNECTED:
        iconPaths = ICON_STATES.CONNECTED;
        break;
      case ConnectionState.CONNECTING:
      case ConnectionState.RECONNECTING:
        iconPaths = ICON_STATES.CONNECTING;
        break;
      case ConnectionState.ERROR:
        iconPaths = ICON_STATES.ERROR;
        break;
      case ConnectionState.DISCONNECTED:
      default:
        iconPaths = ICON_STATES.DISCONNECTED;
        break;
    }

    // 检查图标文件是否存在，如果不存在则使用默认图标
    const iconExists = await checkIconExists(iconPaths[16]);
    if (!iconExists) {
      console.log('状态图标不存在，使用默认图标');
      iconPaths = ICON_STATES.DEFAULT;
    }

    await chrome.action.setIcon({ path: iconPaths });
    console.log('✅ 扩展图标已更新为:', state);
  } catch (error) {
    console.error('❌ 更新扩展图标失败:', error);
    // 回退到默认图标
    try {
      await chrome.action.setIcon({ path: ICON_STATES.DEFAULT });
    } catch (fallbackError) {
      console.error('❌ 设置默认图标也失败:', fallbackError);
    }
  }
}

// 检查图标文件是否存在
async function checkIconExists(iconPath: string): Promise<boolean> {
  try {
    const response = await fetch(chrome.runtime.getURL(iconPath));
    return response.ok;
  } catch {
    return false;
  }
}

// 通知状态变化
async function notifyStateChange(state: ConnectionState, error?: string): Promise<void> {
  connectionState = state;
  console.log('WebSocket状态变化:', state, error ? `错误: ${error}` : '');

  // 更新扩展图标
  await updateExtensionIcon(state);

  // 更新存储的连接状态
  const statusText = getStatusTextFromState(state);
  chrome.storage.sync.set({
    connectionStatus: statusText,
    lastError: error || null
  });

  // 广播状态变化到popup和content script
  chrome.runtime.sendMessage({
    type: 'connectionStatus',
    status: statusText,
    error: error
  }).catch(() => {
    // 忽略错误，popup可能已关闭
  });

  // 通知content script状态变化
  const currentTabId = await getCurrentChatGPTTabId();
  if (currentTabId) {
    chrome.tabs.sendMessage(currentTabId, {
      type: 'WS_STATE_CHANGE',
      state: state,
      url: wsServerUrl,
      error: error
    }).catch(() => {
      // 忽略错误，content script可能未准备好
    });
  }
}

// 计算指数退避延迟
function calculateBackoffDelay(attempt: number): number {
  const delay = Math.min(
    BASE_RECONNECT_INTERVAL * Math.pow(2, attempt),
    MAX_RECONNECT_INTERVAL
  );
  // 添加随机抖动以避免雷群效应
  const jitter = delay * 0.1 * Math.random();
  return Math.floor(delay + jitter);
}

// 启动心跳机制
function startHeartbeat(): void {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
  }

  heartbeatInterval = setInterval(() => {
    if (wsInstance && wsInstance.readyState === WebSocket.OPEN && !isExtensionSuspended) {
      sendHeartbeat();
    }
  }, HEARTBEAT_INTERVAL);

  console.log('✅ 心跳机制已启动');
}

// 停止心跳机制
function stopHeartbeat(): void {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
  if (heartbeatTimeout) {
    clearTimeout(heartbeatTimeout);
    heartbeatTimeout = null;
  }
  console.log('🛑 心跳机制已停止');
}

// 发送心跳消息
function sendHeartbeat(): void {
  if (!wsInstance || wsInstance.readyState !== WebSocket.OPEN) {
    return;
  }

  const heartbeatMessage = {
    type: 'HEARTBEAT',
    id: crypto.randomUUID(),
    timestamp: new Date().toISOString(),
    data: {
      clientType: 'CHATGPT_FORWARD_PLUGIN',
      lastActivity: lastHeartbeatTime
    }
  };

  try {
    wsInstance.send(JSON.stringify(heartbeatMessage));
    lastHeartbeatTime = Date.now();

    // 设置心跳超时检测
    if (heartbeatTimeout) {
      clearTimeout(heartbeatTimeout);
    }

    heartbeatTimeout = setTimeout(() => {
      console.warn('⚠️ 心跳超时，可能连接已断开');
      if (wsInstance && !isManualDisconnect) {
        // 触发重连
        wsInstance.close(1006, '心跳超时');
      }
    }, HEARTBEAT_TIMEOUT);

    console.log('💓 心跳已发送');
  } catch (error) {
    console.error('❌ 发送心跳失败:', error);
  }
}

// 清理定时器
function clearTimeouts(): void {
  if (connectionTimeout) {
    clearTimeout(connectionTimeout);
    connectionTimeout = null;
  }
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
  if (heartbeatTimeout) {
    clearTimeout(heartbeatTimeout);
    heartbeatTimeout = null;
  }
}

// WebSocket事件处理函数
async function handleWebSocketOpen(): Promise<void> {
  console.log('🔗 WebSocket连接已建立:', wsServerUrl);
  clearTimeouts();
  reconnectAttempts = 0;
  lastHeartbeatTime = Date.now();

  // 先发送认证消息
  console.log('🚀 准备发送认证消息');
  const authSuccess = await sendAuthMessage();
  console.log('🔐 认证结果:', authSuccess ? '成功' : '失败');

  if (!authSuccess) {
    console.error('❌ 认证失败，关闭连接');
    if (wsInstance) {
      wsInstance.close();
    }
    return;
  }

  console.log('✅ 认证成功，继续初始化');
  notifyStateChange(ConnectionState.CONNECTED);

  // 启动网络拦截
  startNetworkInterception();

  // 启动心跳机制
  startHeartbeat();

  // 发送初始化消息
  sendInitMessage();
}

function handleWebSocketError(event: Event): void {
  console.error('WebSocket连接错误:', event);
  clearTimeouts();

  // 提供更详细的错误信息
  let errorMessage = 'WebSocket连接错误';

  // 尝试从URL判断可能的错误原因
  try {
    const url = new URL(wsServerUrl);
    if (url.hostname === 'localhost' || url.hostname === '127.0.0.1') {
      errorMessage = '无法连接到本地服务器，请确保WebSocket服务器正在运行';
    } else {
      errorMessage = `无法连接到服务器 ${url.hostname}:${url.port}`;
    }
  } catch (e) {
    errorMessage = '无效的WebSocket服务器地址';
  }

  notifyStateChange(ConnectionState.ERROR, errorMessage);

  // 如果不是手动断开，尝试重连
  if (!isManualDisconnect) {
    scheduleReconnect();
  }
}

function handleWebSocketClose(event: CloseEvent): void {
  console.log('🔌 WebSocket连接已关闭:', event.code, event.reason);
  clearTimeouts();
  stopHeartbeat();

  if (event.code === 1000) {
    // 正常关闭
    notifyStateChange(ConnectionState.DISCONNECTED);
  } else if (event.code === 1008) {
    // 认证失败
    console.error('❌ 认证失败 - 请检查API密钥');
    notifyStateChange(ConnectionState.ERROR, '认证失败 - 请检查API密钥');
    // 认证失败不重连，需要用户手动修复
  } else {
    // 其他异常关闭
    notifyStateChange(ConnectionState.ERROR, `连接关闭: ${event.reason || '未知原因'}`);

    // 如果不是手动断开，尝试重连
    if (!isManualDisconnect) {
      scheduleReconnect();
    }
  }
}

async function handleWebSocketMessage(event: MessageEvent): Promise<void> {
  try {
    console.log('收到WebSocket消息:', event.data);

    // 解析消息
    let parsedMessage;
    try {
      parsedMessage = JSON.parse(event.data);
    } catch (parseError) {
      console.error('解析WebSocket消息失败:', parseError);
      return;
    }

    // 处理心跳响应
    if (parsedMessage.type === 'HEARTBEAT_RESPONSE' || parsedMessage.type === 'PONG') {
      console.log('💓 收到心跳响应');
      if (heartbeatTimeout) {
        clearTimeout(heartbeatTimeout);
        heartbeatTimeout = null;
      }
      return;
    }

    // 检查是否是浏览器控制指令
    if (isBrowserControlCommand(parsedMessage)) {
      console.log('🎮 处理浏览器控制指令:', parsedMessage);
      await handleBrowserControlCommand(parsedMessage as BrowserControlCommand);
      return;
    }

    // 其他消息转发到content script
    const currentTabId = await getCurrentChatGPTTabId();
    if (currentTabId) {
      chrome.tabs.sendMessage(currentTabId, {
        type: 'WS_MESSAGE_FROM_SERVER',
        data: event.data
      }).catch(error => {
        console.error('转发WebSocket消息到content script失败:', error);
      });
    }
  } catch (error) {
    console.error('处理WebSocket消息失败:', error);
  }
}

// 处理浏览器控制指令
async function handleBrowserControlCommand(command: BrowserControlCommand): Promise<void> {
  try {
    console.log('🎮 执行浏览器控制指令:', command.type);
    const response = await browserControlService.handleCommand(command);

    // 发送响应回WebSocket服务器
    const success = sendMessageToServer(response);
    if (success) {
      console.log('✅ 浏览器控制响应已发送');
    } else {
      console.error('❌ 发送浏览器控制响应失败');
    }
  } catch (error) {
    console.error('❌ 处理浏览器控制指令失败:', error);

    // 发送错误响应
    const errorResponse = {
      type: 'BROWSER_CONTROL_RESPONSE',
      id: command.id,
      timestamp: new Date().toISOString(),
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };

    sendMessageToServer(errorResponse);
  }
}

// 调试：检查所有存储内容
async function debugStorage(): Promise<void> {
  return new Promise((resolve) => {
    console.log('🗄️ Background检查同步存储...');
    chrome.storage.sync.get(null, (syncResult) => {
      if (chrome.runtime.lastError) {
        console.warn('⚠️ Background同步存储访问失败:', chrome.runtime.lastError);
      } else {
        console.log('🗄️ Background同步存储内容:', syncResult);
      }

      console.log('🗄️ Background检查本地存储...');
      chrome.storage.local.get(null, (localResult) => {
        if (chrome.runtime.lastError) {
          console.warn('⚠️ Background本地存储访问失败:', chrome.runtime.lastError);
        } else {
          console.log('🗄️ Background本地存储内容:', localResult);
        }
        resolve();
      });
    });
  });
}

// 获取API密钥用于认证
async function getApiKey(): Promise<string | null> {
  // 先检查所有存储内容
  await debugStorage();

  return new Promise((resolve) => {
    // 先尝试sync storage
    chrome.storage.sync.get(['apiKey'], (syncResult) => {
      if (chrome.runtime.lastError || !syncResult.apiKey) {
        console.warn('⚠️ Background sync storage failed or no apiKey, trying local:', chrome.runtime.lastError);
        // Fallback to local storage
        chrome.storage.local.get(['apiKey'], (localResult) => {
          if (chrome.runtime.lastError) {
            console.error('❌ Background both sync and local storage failed:', chrome.runtime.lastError);
            resolve(null);
          } else {
            console.log('🔍 Background从本地存储获取API密钥:', localResult);
            resolve(localResult.apiKey || null);
          }
        });
      } else {
        console.log('� Background从同步存储获取API密钥:', syncResult);
        console.log('�🔑 Background API密钥值:', syncResult.apiKey);
        console.log('🔑 Background API密钥类型:', typeof syncResult.apiKey);
        console.log('🔑 Background API密钥长度:', syncResult.apiKey ? syncResult.apiKey.length : 0);
        resolve(syncResult.apiKey || null);
      }
    });
  });
}

// 发送认证消息
async function sendAuthMessage(): Promise<boolean> {
  console.log('🔍 Background开始发送认证消息...');

  if (!wsInstance || wsInstance.readyState !== WebSocket.OPEN) {
    console.error('❌ WebSocket未连接，无法发送认证消息');
    return false;
  }

  try {
    const apiKey = await getApiKey();
    console.log('🔑 Background获取到API密钥:', apiKey ? '***存在***' : '未找到');

    if (!apiKey) {
      console.warn('⚠️ Background未找到API密钥，认证失败');
      return false; // 如果没有API密钥，认证失败
    }

    const authMessage = {
      type: 'auth',
      id: crypto.randomUUID(),
      api_key: apiKey,
      timestamp: Date.now()
    };

    console.log('📤 Background发送认证消息:', { type: authMessage.type, id: authMessage.id, hasApiKey: !!authMessage.api_key });
    wsInstance.send(JSON.stringify(authMessage));
    console.log('🔐 Background认证消息已发送成功');
    return true;
  } catch (error) {
    console.error('❌ Background发送认证消息失败:', error);
    return false;
  }
}

// 发送初始化消息
function sendInitMessage(): void {
  if (!wsInstance || wsInstance.readyState !== WebSocket.OPEN) {
    return;
  }

  try {
    const initMessage = {
      type: 'INIT',
      id: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      data: {
        clientType: 'CHATGPT_FORWARD_PLUGIN',
        version: '1.0.0',
        url: 'chrome-extension',
        userAgent: 'Chrome Extension'
      }
    };

    wsInstance.send(JSON.stringify(initMessage));
    console.log('📨 Background已发送初始化消息:', initMessage);
  } catch (error) {
    console.error('发送初始化消息失败:', error);
  }
}

// 安排重连
function scheduleReconnect(): void {
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.log('已达到最大重连次数，停止重连');
    const errorMessage = `连接失败，已尝试 ${maxReconnectAttempts} 次重连。请检查服务器状态或稍后重试。`;
    notifyStateChange(ConnectionState.ERROR, errorMessage);
    return;
  }

  // 如果扩展被挂起，延迟重连
  if (isExtensionSuspended) {
    console.log('扩展已挂起，延迟重连');
    setTimeout(() => {
      if (!isManualDisconnect && !isExtensionSuspended) {
        scheduleReconnect();
      }
    }, 5000);
    return;
  }

  reconnectAttempts++;
  const backoffDelay = calculateBackoffDelay(reconnectAttempts - 1);
  const reconnectMessage = `正在进行第 ${reconnectAttempts}/${maxReconnectAttempts} 次重连...`;
  console.log(`安排第 ${reconnectAttempts} 次重连，${backoffDelay}ms 后执行`);
  notifyStateChange(ConnectionState.RECONNECTING, reconnectMessage);

  reconnectTimeout = setTimeout(() => {
    if (!isManualDisconnect && !isExtensionSuspended) {  // 再次检查是否被手动断开或挂起
      console.log(`执行第 ${reconnectAttempts} 次重连`);
      connectWebSocket(wsServerUrl);
    }
  }, backoffDelay);
}

// 实际的WebSocket连接逻辑
function connectWebSocket(url: string): void {
  try {
    notifyStateChange(ConnectionState.CONNECTING);

    // 创建新的WebSocket连接
    wsInstance = new WebSocket(url);

    // 设置连接超时
    connectionTimeout = setTimeout(() => {
      if (wsInstance && wsInstance.readyState === WebSocket.CONNECTING) {
        console.error('WebSocket连接超时');

        // 移除事件监听器以避免触发其他事件
        wsInstance.removeEventListener('open', handleWebSocketOpen);
        wsInstance.removeEventListener('error', handleWebSocketError);
        wsInstance.removeEventListener('close', handleWebSocketClose);
        wsInstance.removeEventListener('message', handleWebSocketMessage);

        // 关闭连接
        wsInstance.close();
        wsInstance = null;

        // 提供详细的超时错误信息
        const timeoutMessage = `连接超时 (${CONNECTION_TIMEOUT/1000}秒)，请检查服务器地址和网络连接`;
        notifyStateChange(ConnectionState.ERROR, timeoutMessage);

        // 如果不是手动断开，尝试重连
        if (!isManualDisconnect) {
          scheduleReconnect();
        }
      }
    }, CONNECTION_TIMEOUT);

    // 绑定事件处理器
    wsInstance.addEventListener('open', handleWebSocketOpen);
    wsInstance.addEventListener('error', handleWebSocketError);
    wsInstance.addEventListener('close', handleWebSocketClose);
    wsInstance.addEventListener('message', handleWebSocketMessage);

  } catch (error) {
    console.error('创建WebSocket连接失败:', error);
    notifyStateChange(ConnectionState.ERROR, `创建连接失败: ${error}`);
  }
}

// 清理WebSocket连接
function cleanupConnection(): void {
  clearTimeouts();
  stopHeartbeat();

  if (wsInstance) {
    wsInstance.removeEventListener('open', handleWebSocketOpen);
    wsInstance.removeEventListener('error', handleWebSocketError);
    wsInstance.removeEventListener('close', handleWebSocketClose);
    wsInstance.removeEventListener('message', handleWebSocketMessage);

    if (wsInstance.readyState === WebSocket.OPEN || wsInstance.readyState === WebSocket.CONNECTING) {
      wsInstance.close();
    }
    wsInstance = null;
  }
}

// 验证WebSocket URL
function validateWebSocketUrl(url: string): { valid: boolean; error?: string } {
  try {
    const parsedUrl = new URL(url);

    if (!parsedUrl.protocol.startsWith('ws')) {
      return { valid: false, error: '请使用 ws:// 或 wss:// 协议' };
    }

    if (!parsedUrl.hostname) {
      return { valid: false, error: '缺少主机名' };
    }

    if (!parsedUrl.port && (parsedUrl.protocol === 'ws:' || parsedUrl.protocol === 'wss:')) {
      // 如果没有指定端口，检查是否是标准端口
      if (parsedUrl.protocol === 'ws:' && parsedUrl.hostname !== 'localhost' && parsedUrl.hostname !== '127.0.0.1') {
        return { valid: false, error: '请指定端口号' };
      }
    }

    return { valid: true };
  } catch (error) {
    return { valid: false, error: '无效的URL格式' };
  }
}

// 初始化WebSocket连接
function initializeWebSocket(url: string): void {
  console.log('初始化WebSocket连接到:', url);

  // 验证URL
  const validation = validateWebSocketUrl(url);
  if (!validation.valid) {
    console.error('WebSocket URL验证失败:', validation.error);
    notifyStateChange(ConnectionState.ERROR, validation.error!);
    return;
  }

  // 清理现有连接
  cleanupConnection();

  // 重置状态
  wsServerUrl = url;
  reconnectAttempts = 0;
  isManualDisconnect = false;

  // 设置持久化定时器
  setupPersistentTimers();

  // 开始连接
  connectWebSocket(url);
}

// 关闭WebSocket连接
function closeWebSocketConnection(): void {
  console.log('手动关闭WebSocket连接');
  isManualDisconnect = true;
  clearTimeouts();

  // 停止网络拦截
  stopNetworkInterception();

  // 清理持久化定时器
  clearPersistentTimers();

  if (wsInstance) {
    wsInstance.close(1000, '用户手动断开');
  }

  cleanupConnection();
  notifyStateChange(ConnectionState.DISCONNECTED);
}

// 发送消息到WebSocket服务器
function sendMessageToServer(message: any): boolean {
  console.log('📡 尝试发送消息到WebSocket服务器:', message);

  if (!wsInstance || wsInstance.readyState !== WebSocket.OPEN) {
    console.error('❌ WebSocket未连接，无法发送消息');
    console.log('WebSocket状态:', wsInstance ? wsInstance.readyState : 'null');
    return false;
  }

  try {
    const jsonMessage = JSON.stringify(message);
    console.log('📤 发送JSON消息:', jsonMessage);
    wsInstance.send(jsonMessage);
    console.log('✅ 消息发送成功');
    return true;
  } catch (error) {
    console.error('❌ 发送消息失败:', error);
    return false;
  }
}

// 检查连接状态
function isWebSocketConnected(): boolean {
  return wsInstance !== null && wsInstance.readyState === WebSocket.OPEN;
}

// 获取当前连接状态
function getCurrentConnectionState(): ConnectionState {
  return connectionState;
}

// 从存储中加载WebSocket服务器地址并自动连接
chrome.storage.sync.get(['wsServerUrl'], (result) => {
  if (result.wsServerUrl) {
    wsServerUrl = result.wsServerUrl;
    console.log('从存储中加载WebSocket服务器地址:', wsServerUrl);

    // 自动建立WebSocket连接
    console.log('自动建立WebSocket连接...');
    initializeWebSocket(wsServerUrl);
  }
});

// 监听标签页更新
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  // 检查是否为ChatGPT标签页
  if (tab.url && (tab.url.includes('chatgpt.com') || tab.url.includes('chat.openai.com'))) {
    if (changeInfo.status === 'complete') {
      console.log('检测到ChatGPT标签页:', tabId);

      // 通知content script当前WebSocket连接状态
      chrome.tabs.sendMessage(tabId, {
        type: 'WS_STATE_CHANGE',
        state: getCurrentConnectionState(),
        url: wsServerUrl
      }).catch(error => {
        console.error('发送WebSocket状态到内容脚本失败:', error);
      });
    }
  }
});


// 监听来自内容脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('收到消息:', message, '来自:', sender);
  
  // 处理消息
  switch (message.type) {
    case 'CONTENT_SCRIPT_LOADED':
      // 内容脚本已加载
      if (sender.tab && sender.tab.id && sender.tab.url &&
          (sender.tab.url.includes('chatgpt.com') || sender.tab.url.includes('chat.openai.com'))) {
        console.log('ChatGPT内容脚本已加载，标签页ID:', sender.tab.id);

        // 发送当前WebSocket连接状态到内容脚本
        chrome.tabs.sendMessage(sender.tab.id, {
          type: 'WS_STATE_CHANGE',
          state: getCurrentConnectionState(),
          url: wsServerUrl
        }).catch(error => {
          console.error('发送WebSocket状态到内容脚本失败:', error);
        });

        sendResponse({
          success: true,
          wsServerUrl,
          connectionState: getCurrentConnectionState(),
          connected: isWebSocketConnected()
        });
      }
      break;
      
    case 'SET_WS_SERVER':
      // 设置WebSocket服务器地址并在background script中建立连接
      console.log('设置WebSocket服务器地址:', message.url);

      // 保存URL到存储
      chrome.storage.sync.set({ wsServerUrl: message.url });

      // 在background script中初始化WebSocket连接
      try {
        initializeWebSocket(message.url);
        sendResponse({ success: true });
      } catch (error) {
        console.error('初始化WebSocket连接失败:', error);
        sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
      }
      break;

    case 'DISCONNECT_WS_SERVER':
      // 在background script中断开WebSocket连接
      try {
        closeWebSocketConnection();
        sendResponse({ success: true });
      } catch (error) {
        console.error('断开WebSocket连接失败:', error);
        sendResponse({ success: false, error: error instanceof Error ? error.message : String(error) });
      }
      break;

    case 'WS_SEND_MESSAGE':
      // 处理来自content script的WebSocket消息发送请求
      console.log('收到WebSocket消息发送请求:', message.data);

      const success = sendMessageToServer(message.data);
      sendResponse({ success });
      break;

    case 'FORWARD_TO_WEBSOCKET':
      // 处理数据转发请求
      console.log('收到数据转发请求:', message.data);

      const forwardSuccess = sendMessageToServer(message.data);
      sendResponse({ success: forwardSuccess });
      break;

    case 'DEVTOOLS_RESPONSE_DATA':
      // 处理来自DevTools的响应数据
      console.log('🔍 [DevTools] 收到响应数据:', message.data);

      const devtoolsSuccess = sendMessageToServer(message.data);
      if (devtoolsSuccess) {
        console.log('✅ [DevTools] 响应数据转发成功');
      } else {
        console.error('❌ [DevTools] 响应数据转发失败');
      }
      sendResponse({ success: devtoolsSuccess });
      break;

    case 'WS_CONNECTION_STATUS':
      // 返回当前WebSocket连接状态
      sendResponse({
        connected: isWebSocketConnected(),
        state: getCurrentConnectionState(),
        url: wsServerUrl
      });
      break;

    case 'FORWARD_TO_CONTENT':
      // 转发消息到内容脚本
      console.log('收到FORWARD_TO_CONTENT请求，消息数据:', message.data);
      getCurrentChatGPTTabId().then(currentTabId => {
        console.log('获取到的ChatGPT标签页ID:', currentTabId);
        if (currentTabId) {
          // 先检查标签页是否存在
          chrome.tabs.get(currentTabId).then(tab => {
            console.log('标签页信息:', { id: tab.id, url: tab.url, status: tab.status });

            // 检查标签页是否完全加载
            if (tab.status !== 'complete') {
              console.log('标签页还在加载中，等待加载完成...');
              sendResponse({ success: false, error: '标签页还在加载中，请稍后重试' });
              return;
            }

            // 先发送一个ping消息检查content script是否准备好
            chrome.tabs.sendMessage(currentTabId, { type: 'PING' })
              .then(() => {
                // Content script准备好了，发送实际消息
                return chrome.tabs.sendMessage(currentTabId, message.data);
              })
              .then(response => {
                console.log('收到content script响应:', response);
                sendResponse(response);
              })
              .catch(error => {
                console.error('转发消息到内容脚本失败:', error);
                console.error('错误详情:', error.message);

                // 提供更具体的错误信息
                let errorMessage = '转发失败';
                if (error.message.includes('Could not establish connection')) {
                  errorMessage = 'Content script未准备好，请确保在ChatGPT页面上操作';
                } else if (error.message.includes('Receiving end does not exist')) {
                  errorMessage = 'Content script未加载，请刷新ChatGPT页面后重试';
                } else {
                  errorMessage = `转发失败: ${error.message}`;
                }

                sendResponse({ success: false, error: errorMessage });
              });
          }).catch(error => {
            console.error('标签页不存在或无法访问:', error);
            sendResponse({ success: false, error: '标签页不存在或无法访问' });
          });
        } else {
          console.log('没有找到ChatGPT标签页');
          sendResponse({ success: false, error: '没有活跃的ChatGPT标签页，请打开ChatGPT页面' });
        }
      }).catch(error => {
        console.error('获取ChatGPT标签页失败:', error);
        sendResponse({ success: false, error: '获取ChatGPT标签页失败' });
      });
      return true; // 异步响应

    case 'OPEN_CHATGPT':
      // 打开ChatGPT页面
      chrome.tabs.create({ url: 'https://chatgpt.com' }, (tab) => {
        if (tab && tab.id) {
          sendResponse({ success: true });
        } else {
          sendResponse({ success: false, error: '创建标签页失败' });
        }
      });
      return true; // 异步响应


      
    default:
      console.warn('未知消息类型:', message.type);
      sendResponse({ success: false, error: '未知消息类型' });
  }
  
  return true; // 表示将异步发送响应
});

// 添加上下文菜单
chrome.runtime.onInstalled.addListener(() => {
  chrome.contextMenus.create({
    id: 'chatgpt-forward',
    title: 'ChatGPT Forward',
    contexts: ['page'],
    documentUrlPatterns: ['*://chatgpt.com/*', '*://chat.openai.com/*']
  });
  
  chrome.contextMenus.create({
    id: 'chatgpt-forward-settings',
    title: '设置',
    parentId: 'chatgpt-forward',
    contexts: ['page'],
    documentUrlPatterns: ['*://chatgpt.com/*', '*://chat.openai.com/*']
  });
});

// 处理上下文菜单点击
chrome.contextMenus.onClicked.addListener((info) => {
  if (info.menuItemId === 'chatgpt-forward-settings') {
    // 打开设置页面
    chrome.runtime.openOptionsPage();
  }
});

// 浏览器生命周期事件处理
// 监听扩展启动事件
chrome.runtime.onStartup.addListener(() => {
  console.log('🚀 浏览器启动，重新初始化WebSocket连接');
  isExtensionSuspended = false;

  // 从存储中恢复连接
  chrome.storage.sync.get(['wsServerUrl'], (result) => {
    if (result.wsServerUrl && !isManualDisconnect) {
      console.log('恢复WebSocket连接到:', result.wsServerUrl);
      // 重置重连计数
      reconnectAttempts = 0;
      initializeWebSocket(result.wsServerUrl);
    }
  });
});

// 监听扩展挂起事件（Manifest V3 service worker）
if (chrome.runtime.onSuspend) {
  chrome.runtime.onSuspend.addListener(() => {
    console.log('⏸️ 扩展即将挂起，保存状态');
    isExtensionSuspended = true;

    // 保存当前连接状态
    chrome.storage.local.set({
      wasConnected: isWebSocketConnected(),
      lastConnectionTime: Date.now(),
      reconnectAttempts: reconnectAttempts,
      wsServerUrl: wsServerUrl
    });

    // 暂停心跳但不关闭连接
    stopHeartbeat();
  });
}

// 监听扩展挂起取消事件
if (chrome.runtime.onSuspendCanceled) {
  chrome.runtime.onSuspendCanceled.addListener(() => {
    console.log('▶️ 扩展挂起已取消，恢复正常运行');
    isExtensionSuspended = false;

    // 如果WebSocket仍然连接，重新启动心跳
    if (wsInstance && wsInstance.readyState === WebSocket.OPEN) {
      startHeartbeat();
    } else if (!isManualDisconnect) {
      // 如果连接已断开，尝试重连
      chrome.storage.local.get(['wasConnected', 'wsServerUrl'], (result) => {
        if (result.wasConnected && result.wsServerUrl) {
          console.log('恢复WebSocket连接');
          initializeWebSocket(result.wsServerUrl);
        }
      });
    }
  });
}

// 监听扩展安装/更新事件
chrome.runtime.onInstalled.addListener((details) => {
  console.log('🔧 扩展已安装/更新:', details.reason);

  if (details.reason === 'install') {
    console.log('首次安装扩展');
  } else if (details.reason === 'update') {
    console.log('扩展已更新，恢复连接状态');
    // 恢复之前的连接状态
    chrome.storage.sync.get(['wsServerUrl'], (result) => {
      if (result.wsServerUrl) {
        console.log('恢复WebSocket连接到:', result.wsServerUrl);
        initializeWebSocket(result.wsServerUrl);
      }
    });
  }
});

// 使用Chrome alarms API进行持久化的连接检查
const CONNECTION_CHECK_ALARM = 'connection-check';
const HEARTBEAT_ALARM = 'heartbeat';

// 创建持久化的定时器
function setupPersistentTimers(): void {
  // 创建连接检查定时器
  chrome.alarms.create(CONNECTION_CHECK_ALARM, {
    delayInMinutes: 1,
    periodInMinutes: 1
  });

  // 创建心跳定时器
  chrome.alarms.create(HEARTBEAT_ALARM, {
    delayInMinutes: 0.5,
    periodInMinutes: 0.5
  });

  console.log('✅ 持久化定时器已设置');
}

// 监听Chrome alarms事件
chrome.alarms.onAlarm.addListener((alarm) => {
  console.log('⏰ 定时器触发:', alarm.name);

  switch (alarm.name) {
    case CONNECTION_CHECK_ALARM:
      handleConnectionCheck();
      break;
    case HEARTBEAT_ALARM:
      handleHeartbeatAlarm();
      break;
  }
});

// 处理连接检查
function handleConnectionCheck(): void {
  if (!isManualDisconnect && !isExtensionSuspended && wsServerUrl) {
    if (!wsInstance || wsInstance.readyState === WebSocket.CLOSED) {
      console.log('🔄 定期检查发现连接已断开，尝试重连');
      // 重置重连计数以避免过度重连
      if (reconnectAttempts > 5) {
        reconnectAttempts = Math.max(0, reconnectAttempts - 2);
      }
      initializeWebSocket(wsServerUrl);
    } else if (wsInstance.readyState === WebSocket.OPEN) {
      // 连接正常，检查是否需要发送心跳
      const timeSinceLastHeartbeat = Date.now() - lastHeartbeatTime;
      if (timeSinceLastHeartbeat > HEARTBEAT_INTERVAL) {
        console.log('🔄 连接检查触发心跳发送');
        sendHeartbeat();
      }
    }
  }
}

// 处理心跳定时器
function handleHeartbeatAlarm(): void {
  if (wsInstance && wsInstance.readyState === WebSocket.OPEN && !isExtensionSuspended) {
    const timeSinceLastHeartbeat = Date.now() - lastHeartbeatTime;
    if (timeSinceLastHeartbeat >= HEARTBEAT_INTERVAL) {
      sendHeartbeat();
    }
  }
}

// 清理持久化定时器
function clearPersistentTimers(): void {
  chrome.alarms.clear(CONNECTION_CHECK_ALARM);
  chrome.alarms.clear(HEARTBEAT_ALARM);
  console.log('🛑 持久化定时器已清理');
}

// 在扩展启动时设置定时器
setupPersistentTimers();

export {};