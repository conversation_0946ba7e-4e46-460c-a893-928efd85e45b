"""
Pydantic models for element query commands and responses
Aligned with browser extension TypeScript interfaces
"""

from typing import Dict, Any, Optional, List, Union, Literal
from pydantic import BaseModel, Field, validator
from datetime import datetime
import uuid


class SelectorOptions(BaseModel):
    """Element selector options"""
    multiple: Optional[bool] = False
    timeout: Optional[int] = 5000
    waitVisible: Optional[bool] = False
    index: Optional[int] = None


class ElementSelector(BaseModel):
    """Element selector definition"""
    type: Literal["xpath", "css", "id", "class", "tag", "text", "attribute"]
    value: str
    options: Optional[SelectorOptions] = None


class ElementAction(BaseModel):
    """Element action definition"""
    type: Literal["click", "input", "getAttribute", "getText", "scroll", "hover", "focus", "blur"]
    params: Optional[Dict[str, Any]] = None


class ElementPosition(BaseModel):
    """Element position information"""
    x: float
    y: float
    width: float
    height: float


class ElementInfo(BaseModel):
    """Element information"""
    tagName: str
    id: Optional[str] = None
    className: Optional[str] = None
    textContent: Optional[str] = None
    attributes: Optional[Dict[str, str]] = None
    position: Optional[ElementPosition] = None
    visible: bool = True
    selector: Optional[str] = None


# Base command model
class BaseCommand(BaseModel):
    """Base command structure aligned with browser extension"""
    type: str
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())

    class Config:
        extra = "allow"  # Allow additional fields


# Element query commands
class ElementQueryCommand(BaseCommand):
    """Element query command - flat structure aligned with browser extension"""
    type: Literal["ELEMENT_QUERY"] = "ELEMENT_QUERY"
    selector: ElementSelector
    actions: Optional[List[ElementAction]] = None


class PageInfoCommand(BaseCommand):
    """Page info command - flat structure aligned with browser extension"""
    type: Literal["PAGE_INFO"] = "PAGE_INFO"
    info: List[Literal["url", "title", "size", "scroll", "viewport", "all"]] = ["all"]


class ElementWatchCommand(BaseCommand):
    """Element watch command - flat structure aligned with browser extension"""
    type: Literal["ELEMENT_WATCH"] = "ELEMENT_WATCH"
    selector: ElementSelector
    events: List[str]
    options: Optional[Dict[str, Any]] = None


class BatchQuery(BaseModel):
    """Single query in batch"""
    id: str
    selector: ElementSelector
    actions: Optional[List[ElementAction]] = None


class BatchElementQueryCommand(BaseCommand):
    """Batch element query command - flat structure aligned with browser extension"""
    type: Literal["BATCH_ELEMENT_QUERY"] = "BATCH_ELEMENT_QUERY"
    queries: List[BatchQuery]


class ElementScreenshotCommand(BaseCommand):
    """Element screenshot command - flat structure aligned with browser extension"""
    type: Literal["ELEMENT_SCREENSHOT"] = "ELEMENT_SCREENSHOT"
    selector: ElementSelector
    options: Optional[Dict[str, Any]] = None


# Response models
class ElementQueryResult(BaseCommand):
    """Element query result"""
    type: Literal["ELEMENT_QUERY_RESULT"] = "ELEMENT_QUERY_RESULT"
    success: bool
    elements: List[ElementInfo]
    count: int
    error: Optional[str] = None
    executionTime: int


class PageInfoResult(BaseCommand):
    """Page info result"""
    type: Literal["PAGE_INFO_RESULT"] = "PAGE_INFO_RESULT"
    success: bool
    pageInfo: Dict[str, Any]
    error: Optional[str] = None


class ElementEventResult(BaseCommand):
    """Element event result"""
    type: Literal["ELEMENT_EVENT_RESULT"] = "ELEMENT_EVENT_RESULT"
    success: bool
    eventType: str
    element: Optional[ElementInfo] = None
    error: Optional[str] = None


class BatchElementQueryResult(BaseCommand):
    """Batch element query result"""
    type: Literal["BATCH_ELEMENT_QUERY_RESULT"] = "BATCH_ELEMENT_QUERY_RESULT"
    success: bool
    results: List[Dict[str, Any]]
    totalExecutionTime: int
    error: Optional[str] = None


class ElementScreenshotResult(BaseCommand):
    """Element screenshot result"""
    type: Literal["ELEMENT_SCREENSHOT_RESULT"] = "ELEMENT_SCREENSHOT_RESULT"
    success: bool
    dataUrl: Optional[str] = None
    element: Optional[ElementInfo] = None
    error: Optional[str] = None


# Union types
ElementCommand = Union[
    ElementQueryCommand,
    PageInfoCommand,
    ElementWatchCommand,
    BatchElementQueryCommand,
    ElementScreenshotCommand
]

ElementResult = Union[
    ElementQueryResult,
    PageInfoResult,
    ElementEventResult,
    BatchElementQueryResult,
    ElementScreenshotResult
]


# Factory functions
def create_element_query_command(
    selector_type: str,
    selector_value: str,
    actions: Optional[List[Dict[str, Any]]] = None,
    options: Optional[Dict[str, Any]] = None
) -> ElementQueryCommand:
    """Create element query command with flat structure"""
    selector = ElementSelector(
        type=selector_type,
        value=selector_value,
        options=SelectorOptions(**options) if options else None
    )
    
    parsed_actions = None
    if actions:
        parsed_actions = [ElementAction(**action) for action in actions]
    
    return ElementQueryCommand(
        selector=selector,
        actions=parsed_actions
    )


def create_page_info_command(
    info_types: Optional[List[str]] = None
) -> PageInfoCommand:
    """Create page info command with flat structure"""
    return PageInfoCommand(
        info=info_types or ["all"]
    )


def create_element_watch_command(
    selector_type: str,
    selector_value: str,
    events: List[str],
    options: Optional[Dict[str, Any]] = None
) -> ElementWatchCommand:
    """Create element watch command with flat structure"""
    selector = ElementSelector(
        type=selector_type,
        value=selector_value
    )
    
    return ElementWatchCommand(
        selector=selector,
        events=events,
        options=options
    )


def create_batch_element_query_command(
    queries: List[Dict[str, Any]]
) -> BatchElementQueryCommand:
    """Create batch element query command with flat structure"""
    parsed_queries = []
    for query in queries:
        selector = ElementSelector(**query["selector"])
        actions = None
        if "actions" in query:
            actions = [ElementAction(**action) for action in query["actions"]]
        
        parsed_queries.append(BatchQuery(
            id=query["id"],
            selector=selector,
            actions=actions
        ))
    
    return BatchElementQueryCommand(queries=parsed_queries)


def create_element_screenshot_command(
    selector_type: str,
    selector_value: str,
    options: Optional[Dict[str, Any]] = None
) -> ElementScreenshotCommand:
    """Create element screenshot command with flat structure"""
    selector = ElementSelector(
        type=selector_type,
        value=selector_value
    )
    
    return ElementScreenshotCommand(
        selector=selector,
        options=options or {"format": "png", "quality": 1.0}
    )
