"""
Conversation Data Parser

Functions for parsing ChatGPT conversation API responses and extracting
structured message data for storage in the database.
"""

import traceback
from datetime import datetime
from typing import Dict, Any, Optional, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..models import Conversation, Message
from ..schemas import ChatGPTConversationData, MessageData
from ...utils.logging import RichLogger

logger = RichLogger(__name__)


def parse_chatgpt_conversation_data(conversation_data: Dict[str, Any]) -> Optional[ChatGPTConversationData]:
    """Parse ChatGPT conversation API response data"""
    try:
        # Validate and parse the conversation data
        parsed_data = ChatGPTConversationData(**conversation_data)
        return parsed_data
    except Exception as e:
        logger.error(f"❌ Error parsing ChatGPT conversation data: {e}")
        logger.debug(f"🔍 Raw data: {conversation_data}")
        return None


async def extract_messages_from_conversation(conversation_data: ChatGPTConversationData) -> List[MessageData]:
    """Extract messages from ChatGPT conversation data"""
    messages = []
    
    try:
        # Traverse the conversation mapping to extract messages
        for node in conversation_data.mapping.values():
            if node.message and node.message.content.parts:
                # Skip system messages that are visually hidden
                if (node.message.metadata.is_visually_hidden_from_conversation or 
                    not node.message.content.parts or 
                    not any(part.strip() for part in node.message.content.parts)):
                    continue
                
                # Create message data
                message_data = MessageData(
                    id=len(messages) + 1,  # Temporary ID, will be replaced when saved to DB
                    conversation_id=conversation_data.conversation_id,
                    role=node.message.author.role,
                    content="\n".join(node.message.content.parts),
                    content_type=node.message.content.content_type,
                    message_id=node.message.id,
                    parent_id=node.parent,
                    model=node.message.metadata.model_slug,
                    created_at=datetime.fromtimestamp(node.message.create_time) if node.message.create_time else datetime.now(),
                    sent_at=datetime.fromtimestamp(node.message.update_time) if node.message.update_time else None,
                    metadata={
                        "weight": node.message.weight,
                        "status": node.message.status,
                        "end_turn": node.message.end_turn,
                        "recipient": node.message.recipient,
                        **node.message.metadata.model_dump(), 
                    }
                )
                messages.append(message_data)
        
        # Sort messages by creation time
        messages.sort(key=lambda m: m.created_at)
        
        logger.debug(f"📝 Extracted {len(messages)} messages from conversation {conversation_data.conversation_id}")
        return messages
        
    except Exception as e:
        logger.error(f"❌ Error extracting messages from conversation: {e}")
        logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
        return []


async def update_conversation_from_api_data(
    conversation_id: str, 
    conversation_data: ChatGPTConversationData,
    db: AsyncSession
) -> bool:
    """Update conversation and messages from ChatGPT API data"""
    try:
        # Get existing conversation
        query = select(Conversation).where(Conversation.id == conversation_id)
        result = await db.execute(query)
        conversation = result.scalar_one_or_none()
        
        if not conversation:
            # Create new conversation
            logger.info(f"📝 Creating new conversation {conversation_id}")
            conversation = Conversation(
                id=conversation_id,
                title=conversation_data.title,
                status="active",
                created_at=datetime.fromtimestamp(conversation_data.create_time),
                updated_at=datetime.fromtimestamp(conversation_data.update_time),
                is_cached=True,
                last_accessed=datetime.now()
            )
            db.add(conversation)
        else:
            # Update existing conversation metadata
            logger.info(f"🔄 Updating existing conversation {conversation_id}")
            conversation.title = conversation_data.title
            conversation.updated_at = datetime.fromtimestamp(conversation_data.update_time)
            conversation.is_cached = True
            conversation.last_accessed = datetime.now()
        
        # Extract and save messages
        messages = await extract_messages_from_conversation(conversation_data)
        
        # Clear existing messages (we'll replace them with fresh data)
        existing_messages_result = await db.execute(
            select(Message).where(Message.conversation_id == conversation_id)
        )
        existing_messages = existing_messages_result.scalars().all()
        
        for msg in existing_messages:
            await db.delete(msg)
        
        # Add new messages
        for msg_data in messages:
            message = Message(
                conversation_id=conversation_id,
                role=msg_data.role,
                content=msg_data.content,
                content_type=msg_data.content_type,
                message_id=msg_data.message_id,
                parent_id=msg_data.parent_id,
                model=msg_data.model,
                created_at=msg_data.created_at,
                sent_at=msg_data.sent_at,
                message_metadata=msg_data.metadata
            )
            db.add(message)
        
        await db.commit()

        logger.success(f"✅ Successfully saved conversation {conversation_id} with {len(messages)} messages")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating conversation from API data: {e}")
        logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
        await db.rollback()
        return False
