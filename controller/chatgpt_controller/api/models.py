"""
Database Models

SQLAlchemy models for conversation management and caching.
"""

import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import String, Text, DateTime, <PERSON><PERSON><PERSON>, Integer, JSON, Index, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from .database import Base


class Conversation(Base):
    """Conversation model for storing ChatGPT conversation metadata"""
    
    __tablename__ = "conversations"
    
    # Primary key
    id: Mapped[str] = mapped_column(String(255), primary_key=True)
    
    # Conversation metadata
    title: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    mode: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # research, reason, search, etc.
    init_prompt: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Status tracking
    status: Mapped[str] = mapped_column(String(50), default="active")  # active, archived, deleted
    is_cached: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_accessed: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # URL and navigation info
    url: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True)
    redirect_url: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True)
    
    # Relationships
    messages: Mapped[List["Message"]] = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    cache_entries: Mapped[List["ConversationCache"]] = relationship("ConversationCache", back_populates="conversation", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index("ix_conversations_status", "status"),
        Index("ix_conversations_created_at", "created_at"),
        Index("ix_conversations_updated_at", "updated_at"),
        Index("ix_conversations_mode", "mode"),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "title": self.title,
            "mode": self.mode,
            "init_prompt": self.init_prompt,
            "status": self.status,
            "is_cached": self.is_cached,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "url": self.url,
            "redirect_url": self.redirect_url,
            "message_count": len(self.messages) if self.messages else 0,
        }


class Message(Base):
    """Message model for storing individual conversation messages"""
    
    __tablename__ = "messages"
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # Foreign key
    conversation_id: Mapped[str] = mapped_column(String(255), ForeignKey("conversations.id"), nullable=False, index=True)
    
    # Message content
    role: Mapped[str] = mapped_column(String(50), nullable=False)  # user, assistant, system
    content: Mapped[str] = mapped_column(Text, nullable=False)
    content_type: Mapped[str] = mapped_column(String(50), default="text")  # text, image, file, etc.
    
    # Message metadata
    message_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # ChatGPT message ID
    parent_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    model: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    sent_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Additional data
    message_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Relationships
    conversation: Mapped["Conversation"] = relationship("Conversation", back_populates="messages")
    
    # Indexes
    __table_args__ = (
        Index("ix_msg_conversation_id", "conversation_id"),
        Index("ix_msg_role", "role"),
        Index("ix_msg_created_at", "created_at"),
        Index("ix_msg_message_id", "message_id"),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "role": self.role,
            "content": self.content,
            "content_type": self.content_type,
            "message_id": self.message_id,
            "parent_id": self.parent_id,
            "model": self.model,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "sent_at": self.sent_at.isoformat() if self.sent_at else None,
            "metadata": self.message_metadata,
        }


class ConversationCache(Base):
    """Cache model for storing conversation content snapshots"""
    
    __tablename__ = "conversation_cache"
    
    # Primary key
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # Foreign key
    conversation_id: Mapped[str] = mapped_column(String(255), ForeignKey("conversations.id"), nullable=False, index=True)
    
    # Cache metadata
    cache_key: Mapped[str] = mapped_column(String(255), nullable=False, index=True)
    content_hash: Mapped[str] = mapped_column(String(64), nullable=False)  # SHA256 hash
    content_size: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Cached content
    raw_content: Mapped[str] = mapped_column(Text, nullable=False)
    parsed_content: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Cache status
    is_valid: Mapped[bool] = mapped_column(Boolean, default=True)
    cache_source: Mapped[str] = mapped_column(String(100), default="api")  # api, websocket, manual
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    last_accessed: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Request metadata
    request_url: Mapped[Optional[str]] = mapped_column(String(1000), nullable=True)
    request_headers: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    response_headers: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Relationships
    conversation: Mapped["Conversation"] = relationship("Conversation", back_populates="cache_entries")
    
    # Indexes
    __table_args__ = (
        Index("ix_cache_conversation_id", "conversation_id"),
        Index("ix_cache_cache_key", "cache_key"),
        Index("ix_cache_content_hash", "content_hash"),
        Index("ix_cache_created_at", "created_at"),
        Index("ix_cache_expires_at", "expires_at"),
        Index("ix_cache_is_valid", "is_valid"),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "conversation_id": self.conversation_id,
            "cache_key": self.cache_key,
            "content_hash": self.content_hash,
            "content_size": self.content_size,
            "is_valid": self.is_valid,
            "cache_source": self.cache_source,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "last_accessed": self.last_accessed.isoformat() if self.last_accessed else None,
            "request_url": self.request_url,
        }
    
    def get_parsed_content(self) -> Dict[str, Any]:
        """Get parsed content, parsing raw content if needed"""
        if self.parsed_content:
            return self.parsed_content
        
        try:
            return json.loads(self.raw_content)
        except (json.JSONDecodeError, TypeError):
            return {"raw": self.raw_content}
